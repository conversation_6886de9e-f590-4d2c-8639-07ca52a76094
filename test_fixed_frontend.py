#!/usr/bin/env python3
"""
测试修复后的前端API调用
"""

import requests
import json

def test_fixed_frontend():
    """测试修复后的前端API调用"""
    base_url = "http://127.0.0.1:8000"
    
    # 登录获取token
    login_data = {
        "email": "<EMAIL>",
        "password": "123456"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result.get("access_token")
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }
            
            print("=== 测试修复后的API端点格式 ===")
            
            # 获取用户的任务列表
            tasks_response = requests.get(f"{base_url}/api/tasks", headers=headers)
            if tasks_response.status_code == 200:
                tasks = tasks_response.json()
                completed_tasks = [task for task in tasks if task.get('status') == 'completed']
                
                print(f"找到 {len(completed_tasks)} 个已完成的任务")
                
                for task in completed_tasks:
                    task_id = task.get('id')
                    task_title = task.get('title', '未命名')
                    
                    print(f"\n--- 测试任务 ID: {task_id}, 标题: {task_title} ---")
                    
                    # 使用正确的API端点格式
                    metadata_url = f"{base_url}/api/audio/{task_id}/metadata"
                    
                    try:
                        metadata_response = requests.get(metadata_url, headers=headers)
                        print(f"元数据API响应状态码: {metadata_response.status_code}")
                        
                        if metadata_response.status_code == 200:
                            metadata = metadata_response.json()
                            print(f"✅ 成功获取元数据!")
                            print(f"  任务ID: {metadata.get('task_id')}")
                            print(f"  标题: {metadata.get('title')}")
                            print(f"  状态: {metadata.get('status')}")
                            print(f"  进度: {metadata.get('progress')}%")
                            print(f"  创建时间: {metadata.get('created_at')}")
                            print(f"  处理模式: {metadata.get('processing_mode')}")
                            print(f"  语音设置: {metadata.get('voice_settings')}")
                            
                        elif metadata_response.status_code == 404:
                            print(f"❌ 404错误: 任务不存在或无权限")
                        else:
                            print(f"❌ 其他错误: {metadata_response.status_code}")
                            try:
                                error_detail = metadata_response.json()
                                print(f"  错误详情: {error_detail}")
                            except:
                                print(f"  错误内容: {metadata_response.text}")
                                
                    except Exception as e:
                        print(f"❌ 请求异常: {str(e)}")
                
                # 测试原来有问题的任务ID
                print(f"\n=== 测试原来有问题的任务ID ===")
                problem_task_ids = [998, 997, 14]
                
                for task_id in problem_task_ids:
                    print(f"\n--- 测试任务 ID: {task_id} ---")
                    metadata_url = f"{base_url}/api/audio/{task_id}/metadata"
                    
                    try:
                        metadata_response = requests.get(metadata_url, headers=headers)
                        print(f"元数据API响应状态码: {metadata_response.status_code}")
                        
                        if metadata_response.status_code == 200:
                            metadata = metadata_response.json()
                            print(f"✅ 成功获取元数据!")
                            print(f"  标题: {metadata.get('title')}")
                        elif metadata_response.status_code == 404:
                            print(f"❌ 404错误: 任务不存在或无权限（这是正常的，因为这些任务属于其他用户）")
                        else:
                            print(f"❌ 其他错误: {metadata_response.status_code}")
                            
                    except Exception as e:
                        print(f"❌ 请求异常: {str(e)}")
                        
            else:
                print(f"❌ 获取任务列表失败: {tasks_response.status_code}")
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

if __name__ == "__main__":
    test_fixed_frontend()
