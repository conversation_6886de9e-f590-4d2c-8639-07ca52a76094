#!/usr/bin/env python3
"""
测试音频元数据API端点
验证修复后的认证问题
"""

import requests
import json

def test_audio_metadata_api():
    """测试音频元数据API"""
    base_url = "http://127.0.0.1:8000"
    
    # 测试用户登录
    print("=== 测试用户登录 ===")
    login_data = {
        "email": "<EMAIL>",
        "password": "123456"  # 假设这是测试密码
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        print(f"登录响应状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result.get("access_token")
            print(f"登录成功，获取到token: {access_token[:20]}...")
            
            # 测试音频元数据API
            print("\n=== 测试音频元数据API ===")
            test_task_ids = [998, 997, 14]
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }
            
            for task_id in test_task_ids:
                print(f"\n--- 测试任务ID: {task_id} ---")
                metadata_url = f"{base_url}/api/audio/metadata/{task_id}"
                
                try:
                    metadata_response = requests.get(metadata_url, headers=headers)
                    print(f"元数据API响应状态码: {metadata_response.status_code}")
                    
                    if metadata_response.status_code == 200:
                        metadata = metadata_response.json()
                        print(f"✅ 成功获取元数据:")
                        print(f"  任务ID: {metadata.get('task_id')}")
                        print(f"  标题: {metadata.get('title')}")
                        print(f"  状态: {metadata.get('status')}")
                        print(f"  进度: {metadata.get('progress')}")
                        print(f"  创建时间: {metadata.get('created_at')}")
                    else:
                        print(f"❌ 元数据API失败: {metadata_response.status_code}")
                        try:
                            error_detail = metadata_response.json()
                            print(f"  错误详情: {error_detail}")
                        except:
                            print(f"  错误内容: {metadata_response.text}")
                            
                except Exception as e:
                    print(f"❌ 请求异常: {str(e)}")
            
            # 测试音频流API（应该正常工作）
            print("\n=== 测试音频流API ===")
            for task_id in test_task_ids:
                print(f"\n--- 测试音频流 任务ID: {task_id} ---")
                stream_url = f"{base_url}/api/audio/{task_id}/stream"
                
                try:
                    # 使用HEAD请求检查文件是否存在
                    stream_response = requests.head(stream_url, headers=headers)
                    print(f"音频流API响应状态码: {stream_response.status_code}")
                    
                    if stream_response.status_code == 200:
                        print(f"✅ 音频流可用")
                        content_length = stream_response.headers.get('content-length')
                        content_type = stream_response.headers.get('content-type')
                        if content_length:
                            print(f"  文件大小: {content_length} bytes")
                        if content_type:
                            print(f"  内容类型: {content_type}")
                    else:
                        print(f"❌ 音频流不可用: {stream_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 音频流请求异常: {str(e)}")
            
            # 测试音频库API
            print("\n=== 测试音频库API ===")
            try:
                library_response = requests.get(f"{base_url}/api/audio-library", headers=headers)
                print(f"音频库API响应状态码: {library_response.status_code}")
                
                if library_response.status_code == 200:
                    library_data = library_response.json()
                    audio_books = library_data.get("audioBooks", [])
                    print(f"✅ 音频库API正常，共 {len(audio_books)} 本音频书")
                    
                    # 显示前几本书的信息
                    for i, book in enumerate(audio_books[:3]):
                        print(f"  书籍 {i+1}: ID={book.get('id')}, 标题={book.get('title')}")
                else:
                    print(f"❌ 音频库API失败: {library_response.status_code}")
                    
            except Exception as e:
                print(f"❌ 音频库API请求异常: {str(e)}")
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            try:
                error_detail = login_response.json()
                print(f"  错误详情: {error_detail}")
            except:
                print(f"  错误内容: {login_response.text}")
                
    except Exception as e:
        print(f"❌ 登录请求异常: {str(e)}")

if __name__ == "__main__":
    test_audio_metadata_api()
