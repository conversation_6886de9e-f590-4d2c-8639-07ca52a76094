#!/usr/bin/env python3
"""
创建测试任务并验证音频元数据API
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.task import Task, TaskStatus, TaskType
from app.models.user import User
import requests
import json
from datetime import datetime

def create_test_task_in_db():
    """在数据库中创建测试任务"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    with SessionLocal() as db:
        # 获取用户ID 5（<EMAIL>）
        user = db.query(User).filter(User.id == 5).first()
        if not user:
            print("❌ 找不到用户ID 5")
            return None
        
        print(f"✅ 找到用户: {user.email}")
        
        # 创建测试任务
        test_task = Task(
            user_id=user.id,
            title="测试音频元数据API",
            original_filename="test_metadata.txt",
            task_type=TaskType.TXT,
            status=TaskStatus.COMPLETED,
            progress=100,
            audio_files=[{
                'filename': 'test_audio.mp3',
                'url': '/files/users/5/tasks/999/test_audio.mp3',
                'title': 'test',
                'duration': 60.0,
                'size': 1024000,
                'format': 'mp3'
            }],
            total_duration=60.0,
            file_size=1024000,
            voice_settings={"voice": "zh-CN-XiaochenMultilingualNeural", "speed": 1.0},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.add(test_task)
        db.commit()
        db.refresh(test_task)
        
        print(f"✅ 创建测试任务，ID: {test_task.id}")
        return test_task.id

def test_metadata_api_with_test_task():
    """使用测试任务验证元数据API"""
    base_url = "http://127.0.0.1:8000"
    
    # 创建测试任务
    task_id = create_test_task_in_db()
    if not task_id:
        return False
    
    # 登录用户
    email = "<EMAIL>"
    password = "123456"
    
    login_data = {
        "email": email,
        "password": password
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            print("✅ 登录成功")
            login_result = login_response.json()
            access_token = login_result.get("access_token")
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }
            
            # 测试元数据API
            print(f"\n=== 测试任务ID {task_id} 的元数据API ===")
            metadata_url = f"{base_url}/api/audio/metadata/{task_id}"
            
            try:
                metadata_response = requests.get(metadata_url, headers=headers)
                print(f"元数据API响应状态码: {metadata_response.status_code}")
                
                if metadata_response.status_code == 200:
                    metadata = metadata_response.json()
                    print(f"✅ 成功获取元数据!")
                    print(f"  任务ID: {metadata.get('task_id')}")
                    print(f"  标题: {metadata.get('title')}")
                    print(f"  状态: {metadata.get('status')}")
                    print(f"  进度: {metadata.get('progress')}")
                    print(f"  创建时间: {metadata.get('created_at')}")
                    print(f"  更新时间: {metadata.get('updated_at')}")
                    print(f"  处理模式: {metadata.get('processing_mode')}")
                    print(f"  语音设置: {metadata.get('voice_settings')}")
                    
                    print("\n🎉 音频元数据API修复成功！")
                    return True
                    
                elif metadata_response.status_code == 404:
                    error_detail = metadata_response.json().get('detail', '未知错误')
                    print(f"❌ 404错误: {error_detail}")
                    print("这可能表示API仍有问题或任务创建失败")
                    return False
                    
                else:
                    print(f"❌ 其他错误: {metadata_response.status_code}")
                    try:
                        error_detail = metadata_response.json()
                        print(f"  错误详情: {error_detail}")
                    except:
                        print(f"  错误内容: {metadata_response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ 元数据API请求异常: {str(e)}")
                return False
                
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登录请求异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_metadata_api_with_test_task()
    if success:
        print("\n✅ 测试通过：音频元数据API修复成功")
    else:
        print("\n❌ 测试失败：音频元数据API仍有问题")
