#!/usr/bin/env python3
"""
调试音频元数据API
直接调用API函数来查看详细错误
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.task import Task, TaskStatus, TaskType
from app.models.user import User
from app.api.audio import get_audio_metadata
from app.core.auth import get_current_active_user
from fastapi import HTTPException
import asyncio

def debug_metadata_api():
    """调试元数据API"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    with SessionLocal() as db:
        print("=== 调试音频元数据API ===")
        
        # 获取用户ID 5
        user = db.query(User).filter(User.id == 5).first()
        if not user:
            print("❌ 找不到用户ID 5")
            return
        
        print(f"✅ 找到用户: {user.email}")
        
        # 检查任务999是否存在
        task = db.query(Task).filter(Task.id == 999).first()
        if task:
            print(f"✅ 找到任务999: 标题={task.title}, 用户ID={task.user_id}, 状态={task.status.value}")
            
            # 检查用户权限
            if task.user_id == user.id:
                print("✅ 用户有权限访问此任务")
                
                # 模拟API调用
                try:
                    # 直接调用数据库查询逻辑
                    query_task = db.query(Task).filter(
                        Task.id == 999,
                        Task.user_id == user.id
                    ).first()
                    
                    if query_task:
                        print("✅ 数据库查询成功")
                        print(f"  任务ID: {query_task.id}")
                        print(f"  标题: {query_task.title}")
                        print(f"  状态: {query_task.status.value}")
                        print(f"  进度: {query_task.progress}")
                        print(f"  创建时间: {query_task.created_at}")
                        print(f"  更新时间: {query_task.updated_at}")
                        print(f"  语音设置: {query_task.voice_settings}")
                        
                        # 构建响应数据
                        response_data = {
                            "task_id": query_task.id,
                            "title": query_task.title,
                            "status": query_task.status.value,
                            "progress": query_task.progress,
                            "created_at": query_task.created_at.isoformat(),
                            "updated_at": query_task.updated_at.isoformat(),
                            "processing_mode": getattr(query_task, 'processing_mode', 'standard'),
                            "voice_settings": query_task.voice_settings or {},
                        }
                        
                        print("✅ 响应数据构建成功:")
                        for key, value in response_data.items():
                            print(f"  {key}: {value}")
                        
                        print("\n🎉 API逻辑应该正常工作！")
                        
                    else:
                        print("❌ 数据库查询失败 - 这就是404的原因")
                        
                        # 检查查询条件
                        print("检查查询条件:")
                        print(f"  Task.id == {999}: {Task.id == 999}")
                        print(f"  Task.user_id == {user.id}: {Task.user_id == user.id}")
                        
                        # 分别检查条件
                        task_by_id = db.query(Task).filter(Task.id == 999).first()
                        if task_by_id:
                            print(f"  ✅ 任务ID 999存在，用户ID: {task_by_id.user_id}")
                        else:
                            print(f"  ❌ 任务ID 999不存在")
                        
                        task_by_user = db.query(Task).filter(Task.user_id == user.id).all()
                        print(f"  用户{user.id}的任务数量: {len(task_by_user)}")
                        for t in task_by_user:
                            print(f"    任务ID: {t.id}, 标题: {t.title}")
                        
                except Exception as e:
                    print(f"❌ API调用异常: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    
            else:
                print(f"❌ 用户无权限访问此任务（任务属于用户{task.user_id}）")
        else:
            print("❌ 任务999不存在")
            
            # 检查数据库中的所有任务
            all_tasks = db.query(Task).all()
            print(f"数据库中共有 {len(all_tasks)} 个任务:")
            for t in all_tasks:
                print(f"  任务ID: {t.id}, 标题: {t.title}, 用户ID: {t.user_id}, 状态: {t.status.value}")

if __name__ == "__main__":
    debug_metadata_api()
