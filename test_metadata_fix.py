#!/usr/bin/env python3
"""
测试音频元数据API修复
使用已知有效的用户凭据
"""

import requests
import json

def test_metadata_api_fix():
    """测试元数据API修复"""
    base_url = "http://127.0.0.1:8000"
    
    # 使用已知有效的用户凭据
    email = "<EMAIL>"
    password = "123456"
    
    print(f"=== 使用用户 {email} 测试 ===")
    
    # 登录
    login_data = {
        "email": email,
        "password": password
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            print("✅ 登录成功")
            login_result = login_response.json()
            access_token = login_result.get("access_token")
            user_info = login_result.get("user", {})
            user_id = user_info.get("id")
            
            print(f"用户ID: {user_id}")
            
            # 获取用户的任务列表
            print("\n=== 获取用户任务列表 ===")
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }
            
            tasks_response = requests.get(f"{base_url}/api/tasks", headers=headers)
            if tasks_response.status_code == 200:
                tasks = tasks_response.json()
                print(f"用户有 {len(tasks)} 个任务")
                
                completed_tasks = [task for task in tasks if task.get('status') == 'completed']
                print(f"其中 {len(completed_tasks)} 个已完成")
                
                if completed_tasks:
                    # 测试前几个已完成任务的元数据API
                    print("\n=== 测试已完成任务的元数据API ===")
                    for i, task in enumerate(completed_tasks[:3]):
                        task_id = task.get('id')
                        task_title = task.get('title', '未命名')
                        
                        print(f"\n--- 任务 {i+1}: ID={task_id}, 标题={task_title} ---")
                        
                        # 测试元数据API
                        metadata_url = f"{base_url}/api/audio/metadata/{task_id}"
                        
                        try:
                            metadata_response = requests.get(metadata_url, headers=headers)
                            print(f"元数据API响应: {metadata_response.status_code}")
                            
                            if metadata_response.status_code == 200:
                                metadata = metadata_response.json()
                                print(f"✅ 成功获取元数据:")
                                print(f"  任务ID: {metadata.get('task_id')}")
                                print(f"  标题: {metadata.get('title')}")
                                print(f"  状态: {metadata.get('status')}")
                                print(f"  进度: {metadata.get('progress')}")
                                print(f"  处理模式: {metadata.get('processing_mode')}")
                            else:
                                print(f"❌ 元数据API失败: {metadata_response.status_code}")
                                try:
                                    error_detail = metadata_response.json()
                                    print(f"  错误详情: {error_detail}")
                                except:
                                    print(f"  错误内容: {metadata_response.text}")
                                    
                        except Exception as e:
                            print(f"❌ 元数据API请求异常: {str(e)}")
                        
                        # 测试音频流API
                        stream_url = f"{base_url}/api/audio/{task_id}/stream"
                        
                        try:
                            stream_response = requests.head(stream_url, headers=headers)
                            print(f"音频流API响应: {stream_response.status_code}")
                            
                            if stream_response.status_code == 200:
                                print(f"✅ 音频流可用")
                            else:
                                print(f"❌ 音频流不可用: {stream_response.status_code}")
                                
                        except Exception as e:
                            print(f"❌ 音频流API请求异常: {str(e)}")
                else:
                    print("❌ 用户没有已完成的任务")
            else:
                print(f"❌ 获取任务列表失败: {tasks_response.status_code}")
            
            # 测试特定任务ID（如果用户有权限）
            print("\n=== 测试特定任务ID（可能无权限） ===")
            test_task_ids = [998, 997, 14]
            
            for task_id in test_task_ids:
                metadata_url = f"{base_url}/api/audio/metadata/{task_id}"
                
                try:
                    metadata_response = requests.get(metadata_url, headers=headers)
                    print(f"任务ID {task_id}: {metadata_response.status_code}")
                    
                    if metadata_response.status_code == 200:
                        print(f"  ✅ 有权限访问")
                    elif metadata_response.status_code == 404:
                        print(f"  ❌ 无权限或任务不存在（这是正常的安全行为）")
                    else:
                        print(f"  ❌ 其他错误: {metadata_response.status_code}")
                        
                except Exception as e:
                    print(f"  ❌ 请求异常: {str(e)}")
            
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            try:
                error_detail = login_response.json()
                print(f"错误详情: {error_detail}")
            except:
                print(f"错误内容: {login_response.text}")
                
    except Exception as e:
        print(f"❌ 登录请求异常: {str(e)}")

if __name__ == "__main__":
    test_metadata_api_fix()
