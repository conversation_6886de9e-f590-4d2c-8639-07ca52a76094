#!/usr/bin/env python3
"""
检查数据库中的任务记录
用于诊断音频元数据API 404错误
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.task import Task, TaskStatus
from app.models.user import User

def check_database():
    """检查数据库中的任务记录"""
    try:
        # 创建数据库连接
        engine = create_engine(settings.database_url, echo=False)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("=== 数据库连接成功 ===")
            print(f"数据库URL: {settings.database_url}")
            
            # 检查用户表
            users = db.query(User).all()
            print(f"\n=== 用户表 ({len(users)} 条记录) ===")
            for user in users:
                print(f"用户ID: {user.id}, 邮箱: {user.email}, 活跃: {user.is_active}")
            
            # 检查任务表
            tasks = db.query(Task).all()
            print(f"\n=== 任务表 ({len(tasks)} 条记录) ===")
            for task in tasks:
                print(f"任务ID: {task.id}, 标题: {task.title}, 状态: {task.status.value}, 用户ID: {task.user_id}")
                print(f"  创建时间: {task.created_at}")
                print(f"  音频文件: {task.audio_files}")
                print(f"  播放列表: {task.playlist_url}")
                print("  ---")
            
            # 检查特定ID的任务
            target_ids = [998, 997, 14]
            print(f"\n=== 检查特定任务ID {target_ids} ===")
            for task_id in target_ids:
                task = db.query(Task).filter(Task.id == task_id).first()
                if task:
                    print(f"任务ID {task_id}: 存在")
                    print(f"  标题: {task.title}")
                    print(f"  状态: {task.status.value}")
                    print(f"  用户ID: {task.user_id}")
                    print(f"  音频文件: {task.audio_files}")
                else:
                    print(f"任务ID {task_id}: 不存在")
            
            # 检查已完成的任务
            completed_tasks = db.query(Task).filter(Task.status == TaskStatus.COMPLETED).all()
            print(f"\n=== 已完成的任务 ({len(completed_tasks)} 条记录) ===")
            for task in completed_tasks:
                print(f"任务ID: {task.id}, 标题: {task.title}, 用户ID: {task.user_id}")
            
            # 检查数据目录中的文件
            print(f"\n=== 检查数据目录 ===")
            data_dir = Path(settings.data_dir)
            if data_dir.exists():
                users_dir = data_dir / "users"
                if users_dir.exists():
                    for user_dir in users_dir.iterdir():
                        if user_dir.is_dir():
                            print(f"用户目录: {user_dir.name}")
                            tasks_dir = user_dir / "tasks"
                            if tasks_dir.exists():
                                for task_dir in tasks_dir.iterdir():
                                    if task_dir.is_dir():
                                        print(f"  任务目录: {task_dir.name}")
                                        # 列出音频文件
                                        audio_files = list(task_dir.glob("*.mp3"))
                                        if audio_files:
                                            print(f"    音频文件: {[f.name for f in audio_files]}")
                else:
                    print("用户目录不存在")
            else:
                print("数据目录不存在")
                
    except Exception as e:
        print(f"检查数据库失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
