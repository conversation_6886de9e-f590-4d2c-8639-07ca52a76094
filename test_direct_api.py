#!/usr/bin/env python3
"""
直接测试API端点，不通过HTTP请求
"""

import requests
import json

def test_direct_api():
    """直接测试API端点"""
    base_url = "http://127.0.0.1:8000"
    
    # 先登录获取token
    login_data = {
        "email": "<EMAIL>",
        "password": "123456"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result.get("access_token")
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }
            
            # 测试不同的URL格式
            test_urls = [
                f"{base_url}/api/audio/999/metadata",
                f"{base_url}/api/audio/metadata/999",  # 这个应该是错误的格式
            ]
            
            for url in test_urls:
                print(f"\n=== 测试URL: {url} ===")
                try:
                    response = requests.get(url, headers=headers)
                    print(f"状态码: {response.status_code}")
                    
                    if response.status_code == 404:
                        print("404错误 - 可能是路由问题")
                    elif response.status_code == 200:
                        print("成功！")
                        data = response.json()
                        print(f"响应数据: {data}")
                    else:
                        print(f"其他状态码: {response.status_code}")
                        print(f"响应内容: {response.text}")
                        
                except Exception as e:
                    print(f"请求异常: {str(e)}")
            
            # 测试其他已知工作的端点
            print(f"\n=== 测试已知工作的端点 ===")
            try:
                tasks_response = requests.get(f"{base_url}/api/tasks", headers=headers)
                print(f"任务列表API状态码: {tasks_response.status_code}")
                
                audio_library_response = requests.get(f"{base_url}/api/audio-library", headers=headers)
                print(f"音频库API状态码: {audio_library_response.status_code}")
                
            except Exception as e:
                print(f"测试其他端点异常: {str(e)}")
                
        else:
            print(f"登录失败: {login_response.status_code}")
            
    except Exception as e:
        print(f"测试异常: {str(e)}")

if __name__ == "__main__":
    test_direct_api()
