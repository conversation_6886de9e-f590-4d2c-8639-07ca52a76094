#!/usr/bin/env python3
"""
使用现有用户测试音频元数据API
"""

import requests
import json

def test_with_different_passwords():
    """尝试不同的密码"""
    base_url = "http://127.0.0.1:8000"
    
    # 尝试不同的用户和密码组合
    test_users = [
        {"email": "<EMAIL>", "passwords": ["123456", "password", "admin", "test123"]},
        {"email": "<EMAIL>", "passwords": ["123456", "password", "admin", "test123"]},
        {"email": "<EMAIL>", "passwords": ["123456", "password", "admin", "test123"]},
        {"email": "<EMAIL>", "passwords": ["123456", "password", "admin", "test123"]}
    ]
    
    for user_info in test_users:
        email = user_info["email"]
        print(f"\n=== 测试用户: {email} ===")
        
        for password in user_info["passwords"]:
            print(f"尝试密码: {password}")
            
            login_data = {
                "email": email,
                "password": password
            }
            
            try:
                login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
                
                if login_response.status_code == 200:
                    print(f"✅ 登录成功！用户: {email}, 密码: {password}")
                    login_result = login_response.json()
                    access_token = login_result.get("access_token")
                    
                    # 测试音频元数据API
                    print("测试音频元数据API...")
                    test_task_ids = [998, 997, 14]
                    
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {access_token}"
                    }
                    
                    for task_id in test_task_ids:
                        metadata_url = f"{base_url}/api/audio/metadata/{task_id}"
                        
                        try:
                            metadata_response = requests.get(metadata_url, headers=headers)
                            print(f"  任务ID {task_id}: {metadata_response.status_code}")
                            
                            if metadata_response.status_code == 200:
                                metadata = metadata_response.json()
                                print(f"    ✅ 成功获取元数据: {metadata.get('title')}")
                            elif metadata_response.status_code == 404:
                                print(f"    ❌ 404错误: {metadata_response.json().get('detail', '未知错误')}")
                            else:
                                print(f"    ❌ 其他错误: {metadata_response.status_code}")
                                
                        except Exception as e:
                            print(f"    ❌ 请求异常: {str(e)}")
                    
                    return  # 找到有效用户后退出
                    
                else:
                    print(f"  ❌ 登录失败: {login_response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 请求异常: {str(e)}")
    
    print("\n❌ 没有找到有效的用户凭据")

if __name__ == "__main__":
    test_with_different_passwords()
