#!/usr/bin/env python3
"""
测试用户ID 4的音频元数据API访问
"""

import requests
import json

def test_user_4_access():
    """测试用户ID 4的访问权限"""
    base_url = "http://127.0.0.1:8000"
    
    # 从数据库检查结果可知，用户ID 4的邮箱是 <EMAIL>
    # 让我尝试一些常见的密码
    email = "<EMAIL>"
    common_passwords = [
        "123456", "password", "admin", "test123", "123123", 
        "qwerty", "111111", "000000", "12345678", "andy123",
        "zhou123", "gmail123", "test", "admin123"
    ]
    
    print(f"=== 尝试登录用户ID 4: {email} ===")
    
    for password in common_passwords:
        print(f"尝试密码: {password}")
        
        login_data = {
            "email": email,
            "password": password
        }
        
        try:
            login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
            
            if login_response.status_code == 200:
                print(f"✅ 登录成功！密码: {password}")
                login_result = login_response.json()
                access_token = login_result.get("access_token")
                user_info = login_result.get("user", {})
                
                print(f"用户信息: ID={user_info.get('id')}, 邮箱={user_info.get('email')}")
                
                # 测试音频元数据API
                print("\n测试音频元数据API...")
                test_task_ids = [998, 997, 14]
                
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}"
                }
                
                success_count = 0
                for task_id in test_task_ids:
                    metadata_url = f"{base_url}/api/audio/metadata/{task_id}"
                    
                    try:
                        metadata_response = requests.get(metadata_url, headers=headers)
                        print(f"  任务ID {task_id}: {metadata_response.status_code}")
                        
                        if metadata_response.status_code == 200:
                            metadata = metadata_response.json()
                            print(f"    ✅ 成功获取元数据:")
                            print(f"      标题: {metadata.get('title')}")
                            print(f"      状态: {metadata.get('status')}")
                            print(f"      进度: {metadata.get('progress')}")
                            success_count += 1
                        elif metadata_response.status_code == 404:
                            error_detail = metadata_response.json().get('detail', '未知错误')
                            print(f"    ❌ 404错误: {error_detail}")
                        else:
                            print(f"    ❌ 其他错误: {metadata_response.status_code}")
                            try:
                                error_detail = metadata_response.json()
                                print(f"      错误详情: {error_detail}")
                            except:
                                print(f"      错误内容: {metadata_response.text}")
                                
                    except Exception as e:
                        print(f"    ❌ 请求异常: {str(e)}")
                
                print(f"\n总结: {success_count}/{len(test_task_ids)} 个API调用成功")
                
                if success_count == len(test_task_ids):
                    print("🎉 所有音频元数据API调用都成功！问题已修复！")
                elif success_count > 0:
                    print("⚠️ 部分API调用成功，可能还有其他问题")
                else:
                    print("❌ 所有API调用都失败，问题仍然存在")
                
                return success_count == len(test_task_ids)
                
            else:
                print(f"  ❌ 登录失败: {login_response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
    
    print(f"\n❌ 无法登录用户 {email}")
    
    # 如果无法登录，尝试创建一个新用户进行测试
    print("\n=== 尝试注册新用户进行测试 ===")
    test_email = "<EMAIL>"
    test_password = "test123456"
    
    register_data = {
        "email": test_email,
        "password": test_password
    }
    
    try:
        register_response = requests.post(f"{base_url}/api/auth/register", json=register_data)
        print(f"注册响应: {register_response.status_code}")
        
        if register_response.status_code in [200, 201]:
            print("✅ 注册成功，尝试登录...")
            
            login_data = {
                "email": test_email,
                "password": test_password
            }
            
            login_response = requests.post(f"{base_url}/api/auth/login", json=login_data)
            if login_response.status_code == 200:
                print("✅ 新用户登录成功")
                # 注意：新用户不会有任务998、997、14的访问权限，这是正常的
                print("注意：新用户没有现有任务的访问权限，这是正常的安全行为")
            else:
                print(f"❌ 新用户登录失败: {login_response.status_code}")
        else:
            print(f"❌ 注册失败: {register_response.status_code}")
            
    except Exception as e:
        print(f"❌ 注册异常: {str(e)}")
    
    return False

if __name__ == "__main__":
    test_user_4_access()
